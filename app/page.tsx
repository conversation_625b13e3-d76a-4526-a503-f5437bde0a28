import classNames from 'classnames';
import Link from 'next/link';
import { BackgroundGradientAnimation } from './components/background-gradient-animation';
import { AtSignIcon } from './components/layouts/icons/at-sign-icon';
import { GithubIcon } from './components/layouts/icons/github-icon';
import { LinkedinIcon } from './components/layouts/icons/linkedin-icon';
import ThemeSwitch from './components/layouts/theme-switch/theme-switch';
import { merryWeather } from './fonts';
import ScrollReveal from './components/animations/scroll-reveal';

export default function Home() {
  return (
    <main className="relative min-h-svh w-screen overflow-hidden">
      <div className="absolute top-4 right-4 z-10">
        <ThemeSwitch />
      </div>
      <BackgroundGradientAnimation>
        <div
          className={classNames('relative min-h-svh', merryWeather.className)}
        >
          <div className="absolute top-[15%] md:top-[25%] max-w-6xl flex-col space-y-8 justify-center px-8 md:px-16 text-shadow-lg lg:ml-14">
            <ScrollReveal direction="up" delay={0.2}>
              <h1 className="font-serif text-xl font-medium md:mr-4 md:text-3xl max-w-4xl mb-8">
                Hi there— I am{' '}
                <span className="font-bold">Viraj Shrivastav</span>, a{' '}
                <span className="italic">forever learner of the craft</span> of{' '}
                <span className="underline">
                  workflow automations, AI tech and software
                </span>{' '}
                with a simple goal: making complexity feel effortless. Welcome
                to my personal portfolio.
              </h1>
            </ScrollReveal>

            <ScrollReveal direction="up" delay={0.4}>
              <section className="relative z-10 mb-8">
                <p className="text-base text-justify max-w-4xl leading-relaxed">
                  My world revolves around dissecting repetitive tasks,
                  designing personalized AI agents, and building utility tools
                  that adapt to your needs, not the other way around. I&apos;m
                  endlessly curious about how the latest advancements in AI can
                  simplify workflows—whether it&apos;s trimming hours off a
                  process, organizing chaos into clarity, or creating a
                  &quot;digital assistant&quot; tailored to unique goals.
                </p>
              </section>
            </ScrollReveal>

            <ScrollReveal direction="up" delay={0.6}>
              <section className="relative z-10 flex space-x-4 items-center text-sm mt-8">
                <div>
                  <p className="mb-3 font-medium">More about me:</p>
                  <div className="flex -ml-2">
                    <Link
                      href="https://www.linkedin.com/in/viraj-shrivastav-086718255/"
                      target="_blank"
                      rel="noreferrer"
                      aria-label="linkedin"
                    >
                      <LinkedinIcon className="h-9 w-9" />
                    </Link>
                    <Link
                      href="https://github.com/virajshrivastav"
                      target="_blank"
                      rel="noreferrer"
                      aria-label="github"
                    >
                      <GithubIcon className="h-9 w-9" />
                    </Link>
                    <Link
                      href="mailto:<EMAIL>"
                      aria-label="email"
                      rel="noreferrer"
                    >
                      <AtSignIcon className="h-9 w-9" />
                    </Link>
                  </div>
                </div>
                <div className="h-14 border-l border-gray-300 mx-6" />
                <div className="flex flex-row space-x-4">
                  <Link
                    href="/about"
                    className="hover-lift transition-all duration-300 hover:text-primary-500 text-base font-medium"
                  >
                    /about
                  </Link>
                  <Link
                    href="/projects"
                    className="hover-lift transition-all duration-300 hover:text-primary-500 text-base font-medium"
                  >
                    /projects
                  </Link>
                  <Link
                    href="/thoughts"
                    className="hover-lift transition-all duration-300 hover:text-primary-500 text-base font-medium"
                  >
                    /thoughts
                  </Link>
                </div>
              </section>
            </ScrollReveal>
          </div>
        </div>
      </BackgroundGradientAnimation>
    </main>
  );
}
