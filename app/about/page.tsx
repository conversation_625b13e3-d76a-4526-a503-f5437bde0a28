import { Fragment } from 'react';
import Image from 'next/image';
import Header from '../components/header';
import ScrollReveal from '../components/animations/scroll-reveal';
import StaggeredReveal from '../components/animations/staggered-reveal';
import FloatingElements from '../components/animations/floating-elements';
import GeometricPattern from '../components/animations/geometric-pattern';
import ThemeSwitchWrapper from '../components/theme-switch-wrapper';

export const metadata = {
  title: 'About',
  description:
    'About Viraj Shrivastav - Technical problem-solver specializing in automation engineering and applied AI',
};

export default function Page() {
  return (
    <Fragment>
      <ThemeSwitchWrapper />
      <Header title="About" />
      <div className="relative">
        <FloatingElements count={8} className="opacity-30" />
        <GeometricPattern density="low" opacity={0.03} />
        <div className="relative z-10 space-y-8">
          <ScrollReveal direction="up" delay={0.2}>
            <div className="flex flex-col lg:flex-row lg:items-start lg:gap-8 space-y-6 lg:space-y-0">
              <ScrollReveal
                direction="left"
                delay={0.4}
                className="flex-shrink-0"
              >
                <div className="hover-lift rounded-lg overflow-hidden">
                  <Image
                    src="/static/images/Informal.jpg"
                    alt="Viraj Shrivastav - Technical problem-solver and AI automation specialist"
                    width={300}
                    height={400}
                    className="rounded-lg shadow-lg object-cover transition-transform duration-300"
                    priority
                  />
                </div>
              </ScrollReveal>
              <ScrollReveal direction="right" delay={0.6} className="flex-1">
                <p
                  className="text-xl md:text-2xl leading-relaxed font-light text-gray-800 dark:text-gray-200 max-w-3xl font-serif"
                  style={{ fontFamily: 'Times New Roman, serif' }}
                >
                  Hi, I&apos;m Viraj—a technical problem-solver with an
                  obsession for efficiency. My work sits at the intersection of
                  automation engineering and applied AI, where I bridge the gap
                  between cutting-edge tools and real-world utility.
                </p>
              </ScrollReveal>
            </div>
          </ScrollReveal>

          <ScrollReveal direction="up" delay={0.8}>
            <div className="space-y-8">
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 font-playball">
                My techstack:
              </h3>
              <StaggeredReveal
                direction="scale"
                staggerDelay={0.1}
                className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-8 justify-items-center"
              >
                <div className="flex flex-col items-center gap-3 min-w-0 tech-icon-hover p-4 rounded-lg">
                  <div className="w-16 h-16 flex items-center justify-center">
                    <Image
                      src="/static/images/tools/python.png"
                      alt="Python"
                      width={64}
                      height={64}
                      className="object-contain max-w-full max-h-full transition-transform duration-300"
                    />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300 text-center font-medium">
                    Python
                  </span>
                </div>
                <div className="flex flex-col items-center gap-3 min-w-0 tech-icon-hover p-4 rounded-lg">
                  <div className="w-16 h-16 flex items-center justify-center">
                    <Image
                      src="/static/images/tools/react.png"
                      alt="React"
                      width={64}
                      height={64}
                      className="object-contain max-w-full max-h-full transition-transform duration-300"
                    />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300 text-center font-medium">
                    React
                  </span>
                </div>
                <div className="flex flex-col items-center gap-3 min-w-0 tech-icon-hover p-4 rounded-lg">
                  <div className="w-16 h-16 flex items-center justify-center">
                    <Image
                      src="/static/images/tools/n8n.webp"
                      alt="n8n"
                      width={64}
                      height={64}
                      className="object-contain max-w-full max-h-full transition-transform duration-300"
                    />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300 text-center font-medium">
                    n8n
                  </span>
                </div>
                <div className="flex flex-col items-center gap-3 min-w-0 tech-icon-hover p-4 rounded-lg">
                  <div className="w-16 h-16 flex items-center justify-center">
                    <Image
                      src="/static/images/tools/aws.png"
                      alt="AWS Lambda"
                      width={64}
                      height={64}
                      className="object-contain max-w-full max-h-full transition-transform duration-300"
                    />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300 text-center font-medium">
                    AWS Lambda
                  </span>
                </div>
                <div className="flex flex-col items-center gap-3 min-w-0 tech-icon-hover p-4 rounded-lg">
                  <div className="w-16 h-16 flex items-center justify-center">
                    <Image
                      src="/static/images/tools/googlecloud.png"
                      alt="Google Cloud Functions"
                      width={64}
                      height={64}
                      className="object-contain max-w-full max-h-full transition-transform duration-300"
                    />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300 text-center font-medium">
                    Google Cloud
                  </span>
                </div>
                <div className="flex flex-col items-center gap-3 min-w-0 tech-icon-hover p-4 rounded-lg">
                  <div className="w-16 h-16 flex items-center justify-center">
                    <Image
                      src="/static/images/tools/chatgpt.png"
                      alt="OpenAI"
                      width={64}
                      height={64}
                      className="object-contain max-w-full max-h-full transition-transform duration-300"
                    />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300 text-center font-medium">
                    OpenAI
                  </span>
                </div>
                <div className="flex flex-col items-center gap-3 min-w-0 tech-icon-hover p-4 rounded-lg">
                  <div className="w-16 h-16 flex items-center justify-center">
                    <Image
                      src="/static/images/tools/claude.png"
                      alt="Claude"
                      width={64}
                      height={64}
                      className="object-contain max-w-full max-h-full transition-transform duration-300"
                    />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300 text-center font-medium">
                    Claude
                  </span>
                </div>
                <div className="flex flex-col items-center gap-3 min-w-0 tech-icon-hover p-4 rounded-lg">
                  <div className="w-16 h-16 flex items-center justify-center">
                    <Image
                      src="/static/images/tools/vscode.png"
                      alt="VS Code"
                      width={64}
                      height={64}
                      className="object-contain max-w-full max-h-full transition-transform duration-300"
                    />
                  </div>
                  <span className="text-sm text-gray-700 dark:text-gray-300 text-center font-medium">
                    VS Code
                  </span>
                </div>
              </StaggeredReveal>
            </div>
          </ScrollReveal>

          <ScrollReveal direction="up" delay={0.2}>
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 font-playball">
                Skills & Approach
              </h2>
              <div className="space-y-4">
                <ScrollReveal direction="up" delay={0.3}>
                  <p
                    className="text-lg md:text-xl leading-relaxed text-gray-700 dark:text-gray-300 font-serif"
                    style={{ fontFamily: 'Times New Roman, serif' }}
                  >
                    I specialize in end-to-end automation architecture:
                    scripting Python-based workflows, designing React-powered
                    UIs, and orchestrating systems via n8n or cloud platforms
                    (AWS, GCP). My AI/ML projects prioritize practicality, not
                    just theoretical models. What excites me most isn&apos;t
                    just building solutions—it&apos;s ensuring they feel
                    invisible, like a natural extension of your workflow.
                    Whether it&apos;s a custom AI agent library for a startup or
                    a no-nonsense automation pipeline for individuals, I focus
                    on outcomes that stick: saved hours, fewer frustrations, and
                    tools that finally fit.
                  </p>
                </ScrollReveal>
                <ScrollReveal direction="up" delay={0.4}>
                  <p
                    className="text-lg md:text-xl leading-relaxed text-gray-700 dark:text-gray-300 font-serif"
                    style={{ fontFamily: 'Times New Roman, serif' }}
                  >
                    Off-duty, I am either hiking a long trail or living rent-free in my friends' heads for my FIFA skills (because I&apos;m too good).
                  </p>
                </ScrollReveal>
              </div>
            </div>
          </ScrollReveal>
        </div>
      </div>
    </Fragment>
  );
}
