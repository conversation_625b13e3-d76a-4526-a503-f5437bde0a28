{"name": "Email Categorisation Agent", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "filters": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [120, 80], "id": "18273a10-3e13-4740-a77b-ae8b54edea8e", "name": "New Email", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.snippet }}", "messages": {"messageValues": [{"message": "=## 🧠 Task\nYou are an email snippet classifier AI.\n\nYour job is to review the provided email snippet and classify it into one of the following 5 categories:\n\n- \"Spam\" – The message appears to be a generic sales pitch, mass outreach, or irrelevant.\n- \"Urgent\" – The message is time-sensitive and requires immediate attention or action.\n- \"Customer Service\" – The sender is asking a question, reporting an issue, or requesting assistance.\n- \"Sales\" – The message presents a potential business, collaboration, or growth opportunity.\n- \"General Response\" – A routine message that is not urgent but still needs a reply.\n\n---\n\n## 🔍 Evaluation Criteria\nUse the following factors to guide your classification:\n\n1. Personalisation – Does it mention the recipient’s name, company, or specific context?  \n2. Intent Clarity – Is the reason for contact clear and specific?  \n3. Sales Language – Is it pushing a product, service, or demo aggressively?  \n4. Urgency – Does the message highlight a deadline, critical issue, or immediate need?  \n5. Customer Service Need – Is the sender asking for help, reporting a problem, or seeking service/support?  \n6. Opportunity/Relevance – Does it offer business potential or align with the recipient’s interests?  \n7. Tone – Is the message friendly and thoughtful, or templated and pushy?\n\n---\n\n## ✅ Output Format:\nRespond with only one of the following exact categories (no explanation):\n\nSpam  \nUrgent  \nCustomer Service  \nOpportunity  \nGeneral Response\n\n---\n\n## 📩 Snippet to Analyse:\n{{ $json.snippet }}"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [520, 80], "id": "73fa7a08-2442-4187-bf81-452238946634", "name": "Check Email"}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('New Email').item.json.id }}", "labelIds": ["Label_8064728961058265043"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [500, -220], "id": "1ba69478-6351-4950-afcf-aa4ee988e8a9", "name": "Move to Loom", "webhookId": "55ae3b12-2a78-4ab1-b96d-f6dfdec1b085", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "48b7598f-3c77-4e98-a2eb-50a37fff09a8", "leftValue": "={{ $json.From }}", "rightValue": "Loom <<EMAIL>>", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [300, 80], "id": "0a5b0173-5b18-4180-8370-7141f20fdaad", "name": "If"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1420, 240], "id": "9f960a37-702a-406b-a300-dd4c5bd25432", "name": "Type of Message", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('New Email').item.json.id }}", "labelIds": ["SPAM"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1120, -160], "id": "f8e4c30c-c0ce-429b-84c3-ef8c42c60af3", "name": "<PERSON> as <PERSON>m", "webhookId": "bcebb951-f1ce-4aca-9cf1-68650c243e5d", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.text }}", "rightValue": "Spam", "operator": {"type": "string", "operation": "equals"}, "id": "1f29149a-372f-431c-ac2a-19b10ad2f086"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Spam"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "30fa9894-9016-4af7-bdc4-56f59af8eb58", "leftValue": "={{ $json.text }}", "rightValue": "<PERSON><PERSON>", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4dfed5df-c7d2-4f7a-8d60-d42c87ff82df", "leftValue": "={{ $json.text }}", "rightValue": "Customer Service", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Customer Service"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2fcca843-6858-4fe2-9096-dbb35b28b06a", "leftValue": "={{ $json.text }}", "rightValue": "Sales", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Sales"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "db82179a-c74c-4910-9e1a-dc1020026e9c", "leftValue": "={{ $json.text }}", "rightValue": "General Response", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "General Response"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [840, 40], "id": "a6afe81b-edaa-430f-8f58-7bad9e1f0710", "name": "Email Type"}, {"parameters": {"promptType": "define", "text": "={{ $json['all snippets'] }}", "messages": {"messageValues": [{"message": "=## 🧠 Task\nYou are an email re-classification AI.\n\nYour job is to review the full email content provided and determine the most suitable classification based on the message's nature and priority.\n\n---\n\n## 🔄 Purpose\nThis is a second-step validation to confirm or update the email’s classification after an initial pass. Use the full context to ensure the correct category is applied.\n\n---\n\n## 🔍 Categories\nChoose only one of the following categories:\n\n- \"Spam\" – The message appears to be a generic sales pitch, mass outreach, or irrelevant.\n- \"Urgent\" – The message is time-sensitive and requires immediate attention or action.\n- \"Customer Service\" – The sender is asking for help with an existing service, reporting an issue, or requesting assistance related to something they already have access to.\n- \"Sales\" – The sender is expressing interest in your services, requesting pricing, asking about your offerings, or inquiring about potential collaboration or new business opportunities.\n- \"General Response\" – A routine message that is not urgent but still needs a reply, and does not clearly fit into the other categories.\n\n---\n\n## 🔍 Evaluation Criteria\nUse the following factors to guide your classification:\n\n1. Service Interest vs. Service Support:\n   - If the sender is asking for details about your services (e.g., pricing, offerings, processes), classify as Opportunity.\n   - If the sender is asking for help with an existing service or account, classify as Customer Service.\n\n2. Intent Clarity:\n   - Is the sender looking to buy, explore, or collaborate? (Opportunity)\n   - Is the sender reporting an issue or asking for support? (Customer Service)\n\n3. Urgency:\n   - Is there a deadline, critical issue, or immediate need? (Urgent)\n\n4. Spam Indicators:\n   - Does the message look like a generic or irrelevant sales pitch?\n\n5. Tone & Relevance:\n   - Is the message thoughtful, personalised, and aligned with your business?\n\n---\n\n## ✅ Output Format:\nRespond with only one of the following exact categories (no explanation):\n\nSpam  \nUrgent  \nCustomer Service  \nSales  \nGeneral Response\n\n---\n\n## 📩 Full Email to Analyse:\n{{ $json['all snippets'] }}"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [1420, 100], "id": "baa4f3ab-b18c-4e8a-b710-a7eb64539446", "name": "Status Validator"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [500, 220], "id": "4ceab7eb-de29-490f-a70a-e6a0e1070583", "name": "Check Email <PERSON>", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"resource": "thread", "operation": "get", "threadId": "={{ $('New Email').item.json.threadId }}", "simple": false, "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1100, 100], "id": "b5faedfc-15cc-4f8c-a928-9bc13efb8708", "name": "Get Threads", "webhookId": "5e7099c8-e4ac-4960-ab90-0fe695eb9ec4", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2000, -240], "id": "5c9e7d23-72b2-4fb6-9cee-af3f12fa0ff2", "name": "Response Brain", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"promptType": "define", "text": "={{ $('All Snippets').item.json['all snippets'] }}", "messages": {"messageValues": [{"message": "=## Task\nYou are a professional email drafting AI.\n\nYour job is to draft a clear, concise, and polite reply to the latest email in an ongoing conversation thread. Use the full context of the thread to ensure the response is relevant and consistent with prior messages.\n\n---\n\n## Style & Tone Guidelines:\n- Maintain a professional, approachable, and confident tone.  \n- Keep responses concise and to the point (avoid unnecessary filler).  \n- Ensure language is consistent with a businesslike tone of voice that is proactive and solutions-focused.  \n- Minimise the use of the word \"I\" wherever possible (reframe sentences to sound collaborative or neutral).  \n- Ensure clarity on next steps or provide helpful information, depending on the nature of the thread.\n\n---\n\n## Output Format:\nRespond with only the draft email body text (no greetings or sign-offs).\n\n---\n\n## 📩 Conversation Thread:\n{{ $('All Snippets').item.json['all snippets'] }}"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [2020, -400], "id": "e4cfd2c7-137e-424f-832d-d9115ddae33e", "name": "Draft Response"}, {"parameters": {"assignments": {"assignments": [{"id": "69d748df-a7d8-4221-bc3a-7c5b04fead22", "name": "all snippets", "value": "={{ $json.messages.map(message => message.snippet).join('\\n\\n') }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1260, 100], "id": "75c9cb63-1ff6-474b-abc6-f6368d919e09", "name": "All Snippets"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6443e7ef-3b30-4a02-8913-e5c8ba98282a", "leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2720, -580], "id": "86d290b1-2386-41b7-979b-252090d61c97", "name": "Send Email?"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "30fa9894-9016-4af7-bdc4-56f59af8eb58", "leftValue": "={{ $('Status Validator').item.json.text }}", "rightValue": "<PERSON><PERSON>", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "db82179a-c74c-4910-9e1a-dc1020026e9c", "leftValue": "={{ $('Status Validator').item.json.text }}", "rightValue": "General Response", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "General Response"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4dfed5df-c7d2-4f7a-8d60-d42c87ff82df", "leftValue": "={{ $('Status Validator').item.json.text }}", "rightValue": "Customer Service", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Customer Service"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2fcca843-6858-4fe2-9096-dbb35b28b06a", "leftValue": "={{ $('Status Validator').item.json.text }}", "rightValue": "Sales", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Sales"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [1760, 80], "id": "6e5711f3-a3f1-45bc-93f3-024c45d8ab6c", "name": "Validated Category"}, {"parameters": {"operation": "reply", "messageId": "={{ $('New Email').item.json.id }}", "emailType": "text", "message": "={{ $('Draft Response').item.json.text }}\n\nBest regards,\n[Your Name]", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2900, -660], "id": "aeeae20d-61f3-44b0-96f7-76d70074ab38", "name": "Send URGENT Response", "webhookId": "5cac925c-bc30-4c5b-8283-c0e674ce80f2", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"resource": "draft", "subject": "={{ $('New Email').item.json.Subject }}", "message": "={{ $('Draft Response').item.json.text }}\n\nBest regards,\n[Your Name]", "options": {"replyTo": "={{ $('New Email').item.json.From }}", "threadId": "={{ $('New Email').item.json.threadId }}", "sendTo": "={{ $('New Email').item.json.From }}"}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2900, -500], "id": "46d09fc1-68c4-4e4b-a91b-772c11f06459", "name": "Create URGENT Draft", "webhookId": "d1840501-d4b1-4594-81ed-d0406bb9ca2e", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08R4CS0XTL", "mode": "list", "cachedResultName": "email-agent"}, "text": "=Message has been sent back to {{ $('New Email').item.json.From }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3120, -660], "id": "9fbf9c47-f37d-493c-b03e-405f821dcc0a", "name": "Confirm URGENT Email Sent", "webhookId": "1b50b5a8-4dd7-4449-869a-5bceb97dbd29", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08R4CS0XTL", "mode": "list", "cachedResultName": "email-agent"}, "text": "=You can find the draft email here for you to fine tune it: https://mail.google.com/mail/u/0/#all/{{ $json.message.id }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3120, -500], "id": "1ba59469-f0c4-4921-b03d-a1bb6b1feaf1", "name": "Share URGENT Draft Link", "webhookId": "1b50b5a8-4dd7-4449-869a-5bceb97dbd29", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"operation": "sendAndWait", "select": "channel", "channelId": {"__rl": true, "value": "C08R4CS0XTL", "mode": "list", "cachedResultName": "email-agent"}, "message": "=🔴 URGENT response: You have an email from {{ $('Get message details').item.json.from.value[0].name }}. This is about: {{ $('New Email').item.json.Subject }}\n\nProposed Draft Response:\n{{ $json.text }}", "approvalOptions": {"values": {"approvalType": "double", "approveLabel": "SEND", "disapproveLabel": "DRAFT"}}, "options": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2540, -580], "id": "dc1589cc-e79a-44fe-a55d-e864c4fc04d7", "name": "URGENT Request", "webhookId": "fde8b016-8b6f-4026-b852-735c51c5202c", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"operation": "sendAndWait", "select": "channel", "channelId": {"__rl": true, "value": "C08R4CS0XTL", "mode": "list", "cachedResultName": "email-agent"}, "message": "=🟢 You have an email from {{ $('Get message details').item.json.from.value[0].name }}. This is about: {{ $('New Email').item.json.Subject }}\n\nProposed Draft Response:\n{{ $json.text }}", "approvalOptions": {"values": {"approvalType": "double", "approveLabel": "SEND", "disapproveLabel": "DRAFT"}}, "options": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2540, -200], "id": "fef033da-a23f-4bda-9629-919c83c6dd27", "name": "Notify User", "webhookId": "fde8b016-8b6f-4026-b852-735c51c5202c", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08R4CS0XTL", "mode": "list", "cachedResultName": "email-agent"}, "text": "=You can find the draft email here for you to fine tune it: https://mail.google.com/mail/u/0/#all/{{ $json.message.id }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3120, -120], "id": "0a1cb828-3589-4117-83d2-ec2eb4656acf", "name": "Share Draft Link", "webhookId": "1b50b5a8-4dd7-4449-869a-5bceb97dbd29", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08R4CS0XTL", "mode": "list", "cachedResultName": "email-agent"}, "text": "=Message has been sent back to {{ $('New Email').item.json.From }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3120, -280], "id": "07206cfb-9d64-4131-bf7b-2e3f88eb5124", "name": "Confirm Message Sent", "webhookId": "1b50b5a8-4dd7-4449-869a-5bceb97dbd29", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"resource": "draft", "subject": "={{ $('New Email').item.json.Subject }}", "message": "={{ $('Draft Response1').item.json.text }}\n\nMany Thanks,\n[Your Name]", "options": {"replyTo": "={{ $('New Email').item.json.From }}", "threadId": "={{ $('New Email').item.json.threadId }}", "sendTo": "={{ $('New Email').item.json.From }}"}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2900, -120], "id": "ba059cbf-bda6-4d08-92da-91acd4dd68b0", "name": "Create Draft", "webhookId": "d1840501-d4b1-4594-81ed-d0406bb9ca2e", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"operation": "reply", "messageId": "={{ $('New Email').item.json.id }}", "emailType": "text", "message": "={{ $('Draft Response1').item.json.text }}\n\nBest regards,\n[Your Name]", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2900, -280], "id": "dcad021f-494e-4541-a27c-be0d6ad88da2", "name": "Send Response", "webhookId": "5cac925c-bc30-4c5b-8283-c0e674ce80f2", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6443e7ef-3b30-4a02-8913-e5c8ba98282a", "leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2720, -200], "id": "da1e2112-198a-4f1e-9d7d-062292e8f92b", "name": "Send Email?1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8efc6cc0-19bf-4fac-bd2d-3fbe0ce16a54", "leftValue": "={{ $('Validated Category').item.json.text }}", "rightValue": "<PERSON><PERSON>", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2360, -400], "id": "270aeec5-71f5-4ce7-b5fb-6b1690c4b79b", "name": "Urgent?"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2000, 280], "id": "2615cd22-04a4-4ec8-a81f-aa0c5a7f4cff", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"promptType": "define", "text": "=## 🧠 Task\nYou are a customer sentiment analysis AI.\n\nYour job is to review the provided email and classify the customer's tone and risk level to help prioritise the response.\n\n---\n\n## 🔍 Classification Categories:\n\n- \"High Risk Customer\" – The customer appears angry, frustrated, or dissatisfied; the message contains complaints, threats to leave, negative language, or indicates potential escalation.\n- \"Normal Customer\" – The customer is polite, neutral, or making a routine request without signs of anger or serious dissatisfaction.\n\n---\n\n## 🔍 Evaluation Criteria:\n\n1. Tone of Voice\n   - High Risk: Uses angry, aggressive, or demanding language (e.g., \"unacceptable\", \"outraged\", \"furious\").\n   - Normal: Uses polite or neutral language (e.g., \"could you help with…\", \"I have a quick question\").\n\n2. Severity\n   - High Risk: Mentions cancelling service, making formal complaints, legal threats, or serious consequences (e.g., \"I will be taking my business elsewhere\", \"this is the last straw\").\n   - Normal: Routine queries or minor issues (e.g., \"I can't log in\", \"just wanted to check on my order\").\n\n3. Request Type\n   - High Risk: Major service failure, repeated unresolved issues, or urgent demands.\n   - Normal: Standard service requests, basic troubleshooting, or account queries.\n\n4. Sentiment Strength\n   - High Risk: Strong negative emotion or frustration is evident throughout the message.\n   - Normal: No significant emotional language, or even positive/neutral sentiment.\n\n---\n\n## 📝 Examples:\n\n- Example 1:\n  \"This is absolutely ridiculous. I’ve contacted you 3 times already and nothing has been done. If this isn't fixed today, I’m cancelling my account.\"  \n  → High Risk Customer\n\n- Example 2:\n  \"Hi, I’m having trouble accessing my account. Could you please reset my password?\"  \n  → Normal Customer\n\n- Example 3:\n  \"I’ve been waiting for a response for over a week. This is not the level of service I expect, and I’m really disappointed.\"  \n  → High Risk Customer\n\n- Example 4:\n  \"Just checking if my recent payment has been processed. Thanks!\"  \n  → Normal Customer\n\n---\n\n## ✅ Output Format:\nRespond with only one of the following exact categories (no explanation):\n\nHigh Risk Customer  \nNormal Customer\n\n---\n\n## 📩 Email to Analyse:\n{{ $('All Snippets').item.json['all snippets'] }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [2040, 140], "id": "8fbf477f-b8d6-4bc2-80e7-0f07e1fcd0ef", "name": "Categorise Issue"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "af6bf327-789f-4815-8394-9d333afb3bcb", "leftValue": "={{ $json.text }}", "rightValue": "High Risk Customer", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2360, 140], "id": "20a8cc59-2f04-4db9-975b-d5abbb9d7ddb", "name": "High Risk?"}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08R4CS0XTL", "mode": "list", "cachedResultName": "email-agent"}, "text": "=You have a high-risk/angry customer. Here is what they have emailed in about: {{ $('New Email').item.json.Subject }}\n\n\nHere is the link to the email chain: https://mail.google.com/mail/u/0/#all/{{ $('New Email').item.json.id }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2580, 80], "id": "28a2bcb9-47d2-4510-bc7a-81bf6e4dec28", "name": "Notify Team To Respond", "webhookId": "b9113f32-016f-49e5-a786-a3a681d895b3", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "GPT-4.1-MINI"}, "messages": {"values": [{"content": "=## 🧠 Task\nYou are a customer support response AI.\n\nYour job is to review the customer's request, identify the key question or issue, and respond only if the information required is fully available within your provided context or the linked knowledge base (vector database).\n\nYou must strictly use the exact wording provided in the knowledge base or your prompt (within authorised guidelines). Do not generate new answers, rephrase, or guess.\n\nIf step-by-step guidance, troubleshooting steps, or instructions exist in the authorised knowledge base, you MUST provide them exactly as written. Do not default to escalation unless the knowledge base explicitly instructs you to escalate for this scenario.\n\nIf you do not have sufficient authorised information to provide a valid response, output:\n\nRequires Human\n\n---\n\n## 🔍 Process:\n\n1. Analyse the Request:\n   - Understand the customer’s question, issue, or request.\n\n2. Check Existing Context:\n   - See if the answer (especially troubleshooting steps or guidance) is fully available within your current prompt/context.\n\n3. Search Knowledge Base:\n   - If needed, search the provided knowledge base (vector database) to find the exact, authorised answer.\n\n4. Respond:\n   - Use only the exact, authorised wording (especially step-by-step instructions, if available).\n   - If multiple knowledge base entries are relevant, select the most specific and relevant one.\n\n5. Fallback:\n   - If no complete authorised answer is available, output:\n     Requires Human\n\n---\n\n## 🚫 Strict Rules:\n\n- Do not create new wording outside of the authorised material.\n- Do not make assumptions, predictions, or provide general advice unless explicitly stated in the authorised guidelines.\n- Do not combine unauthorised knowledge with authorised content.\n- Do not escalate, apologise, or say \"you will be contacted\" unless the knowledge base explicitly says to do so for this case.\n- Always prioritise providing concrete steps or guidance if available in the knowledge base.\n\n---\n\n## ✅ Output Format:\n\nIf a valid response is available, output the full email body as HTML, structured as follows:\n\n<p>Hi customer first name </p>\n\n<p>authorised response</p>\n\n<p>Kind regards,<br>\n\n---\n\nWhere:\n\n- customer_name = Only use their first name (do not include the entire name); use:  \n  `{{ $('New Email').item.json.From }}`\n\n- authorised_response = The exact authorised answer (with HTML formatting if available)\n\nIf no valid response is available, output:\n\nRequires Human\n\n---\n\n📩 Customer Request:\n{{ $('All Snippets1').item.json['all snippets'] }}\n", "role": "assistant"}, {"content": "=Here is the users message:\n{{ $('All Snippets1').item.json['all snippets'] }}"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2560, 260], "id": "e4448858-4415-43e8-93df-1f11db7bef2c", "name": "Response Generator", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [3020, 540], "id": "e30a80c5-2827-442f-bb81-2f6f9ee61c3c", "name": "Q&A Brain", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2700, 680], "id": "b713b61d-1c50-4bd3-a1fc-6fdc0e24dcf4", "name": "Small Embeddings", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"pineconeIndex": {"__rl": true, "value": "customer-service", "mode": "list", "cachedResultName": "customer-service"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [2720, 540], "id": "abbd2b5a-2d8d-4ecc-af52-fdc45db8fd71", "name": "Pinecone Q&A Store", "credentials": {"pineconeApi": {"id": "GFlwfV3RkjziPWtU", "name": "Agera AI Pinecone"}}}, {"parameters": {"description": "Customer service knowledge base to be able to provide responses back to customers"}, "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1.1, "position": [2800, 420], "id": "e1b99fef-cc97-40b8-82e9-f70e1b0d8ca0", "name": "QA Vector Store"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8d37bf83-e436-4f0a-9709-85673e3d1d1a", "leftValue": "={{ $json.message.content }}", "rightValue": "Requires Human", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2900, 120], "id": "5190f7fd-aad8-45ed-82ee-2df7e0688096", "name": "Require Human?"}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08R4CS0XTL", "mode": "list", "cachedResultName": "email-agent"}, "text": "=You need to respond to the following customer as I do not have an authorised response. Here is what they have emailed in about: {{ $('New Email').item.json.Subject }}\n\nHere is the link to the email chain: https://mail.google.com/mail/u/0/#all/{{ $('New Email').item.json.id }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3140, 120], "id": "3b6daa5a-f20a-404c-b9ca-8a6d2602d174", "name": "Human Required", "webhookId": "b9113f32-016f-49e5-a786-a3a681d895b3", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "customer-service", "mode": "list", "cachedResultName": "customer-service"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [2220, 460], "id": "9fda800d-96f7-4f1b-8bd8-9050d1d75406", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": {"id": "GFlwfV3RkjziPWtU", "name": "Agera AI Pinecone"}}}, {"parameters": {"dataType": "binary", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [2180, 640], "id": "4b973550-8d94-4141-8027-49dcbfd0bbbf", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2040, 620], "id": "1c19dfa2-256c-4a7e-89c0-1d28c9aaf86f", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [2460, 640], "id": "72c570ea-afab-48f7-84f7-24772aee467d", "name": "Recursive Character Text Splitter"}, {"parameters": {"formTitle": "Knowledge Base Documents", "formFields": {"values": [{"fieldLabel": "Knowledge Base Documents", "fieldType": "file"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [2020, 460], "id": "f1bb0ffc-e747-40c3-9d40-696d487429bc", "name": "On form submission", "webhookId": "badfa579-bc13-4ac4-b4a4-75a5d012edd7"}, {"parameters": {"operation": "reply", "messageId": "={{ $('New Email').item.json.id }}", "message": "={{ $json.message.content }}\n\n<br> Many Thanks,<br>\nCustomer Support Team", "options": {"replyToSenderOnly": true}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [3140, 320], "id": "96ef52b5-5d5b-4df2-b66c-22d5db8d8693", "name": "Respond to Customer", "webhookId": "b9a38785-8b85-432c-b014-7ee3f1a1ff44", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"content": "## **Get new email**", "height": 260, "width": 200, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [80, 0], "id": "f020b24d-2e4c-4fe9-a2f5-acab246a436f", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## **Mark as Spam**", "height": 240, "width": 200, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1080, -240], "id": "ae5a782c-3aa4-4a80-88c3-5eade0cfc1c1", "name": "Sticky Note1"}, {"parameters": {"content": "## **Label Specific Emails Automatically**", "height": 260, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [460, -320], "id": "030b3d94-f6eb-43e8-b83c-280d7889a8af", "name": "Sticky Note2"}, {"parameters": {"content": "## **Check Type of Email**", "height": 380, "width": 360, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [460, -40], "id": "276e2efc-b4e0-45cb-8fe0-58aa7256e1c9", "name": "Sticky Note3"}, {"parameters": {"content": "## **Check Message Details**", "height": 340, "width": 640, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1080, 20], "id": "e478a0de-30c7-4328-a22e-439993d00100", "name": "Sticky Note4"}, {"parameters": {"content": "## **Auto Generate Email Response**", "height": 720, "width": 1360}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1960, -680], "id": "0d12eb40-5a30-41c0-bda1-e3deb90a762f", "name": "Sticky Note5"}, {"parameters": {"content": "## **Store Guidance Documents**", "height": 380, "width": 660, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1980, 400], "id": "b077c0ce-77d2-4d19-9467-4d90758690b3", "name": "Sticky Note9"}, {"parameters": {"content": "## **Customer Service Response**", "height": 740, "width": 1360, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1960, 60], "id": "828bc064-ec21-4c94-b7ee-0154848457c6", "name": "Sticky Note6"}, {"parameters": {"content": "## **Urgent Response Required**", "height": 340, "width": 800, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2500, -680], "id": "0642d36d-c7ca-4ddb-b054-9c5c6bdf8219", "name": "Sticky Note10"}, {"parameters": {"content": "## **Sales Response**", "height": 340, "width": 1060, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1960, 820], "id": "a10d43b1-b30d-4008-8347-8be4404be958", "name": "Sticky Note7"}, {"parameters": {"operation": "reply", "messageId": "={{ $('New Email').item.json.id }}", "message": "={{ $json.text }}\n\n<br>Many Thanks,<br>\nSales Team", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [2360, 880], "id": "8c8a414c-a5f5-42a9-afda-4baab92112cf", "name": "Respond to Lead", "webhookId": "1bce6ad4-1d02-47d7-bfbb-a5bdf0f8e47d", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIAL_ID", "name": "Gmail Integration"}}}, {"parameters": {"promptType": "define", "text": "=## 🧠 Task\nYou are a notification AI.\n\nYour job is to review the incoming opportunity/inquiry and provide a concise summary for the internal team to be notified about the new lead.\n\nThe summary should:\n\n- Clearly state the nature of the inquiry/opportunity.  \n- Include any service(s) mentioned by the sender.  \n- Note the sender’s name and company if available.  \n- Be brief, actionable, and easy to scan quickly.\n\n---\n\n## ✅ Output Format:\n\nNew Opportunity Notification:\n\n- Sender: Customer Name\n- Service(s) Mentioned: Services offered in email\n- Summary: summary of customer's message plus the email response\n- Suggested Next Step: Review and follow up as needed. Lead is classified as (Hot/Warm/Cold).\n\n---\n\n## 🚫 Strict Rules:\n\n- Keep the summary short and clear (1–2 sentences max).  \n- If no company name is available, omit the parentheses.  \n- If no specific service is mentioned, write: “No specific service mentioned.”  \n- Always state the lead classification (Hot, Warm, or Cold).  \n- Do not generate extra commentary—just the notification in the format above.\n\n---\n\n## AI Agent Email to Summarise:\n{{ $('Sales Message').item.json.text }}\n\n---\n##Email chain with customer:\n{{ $('All Snippets').item.json['all snippets'] }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [2520, 880], "id": "7572d96d-8142-4757-854a-4a732e47b010", "name": "Summarise Request"}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08RRA0654G", "mode": "list", "cachedResultName": "email-leads"}, "text": "={{ $json.text }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [2840, 880], "id": "2074ec1d-0cb0-4780-a823-2124594d56bd", "name": "Notify Team", "webhookId": "c4a2aff5-0989-450d-9808-c6407623d1f7", "credentials": {"slackApi": {"id": "TfkZYwtKVv6KVC62", "name": "Email Agent"}}}, {"parameters": {"promptType": "define", "text": "=## 🧠 Task\nYou are a lead engagement AI.\n\nYour job is to analyse the incoming inquiry and classify it as Cold, Warm, or Hot based on the message content. Then, generate a full HTML-formatted email response based on the classification:\n\n- **Cold Lead:**  \n  Introduce all available services clearly and encourage the recipient to book a session using the provided booking link.\n\n- **Warm/Hot Lead:**  \n  If the inquiry mentions a specific service, focus the reply on that service by providing more detailed information and inviting the recipient to book a session.\n\n---\n\n## 🔍 Services Offered (Dummy Data for Guidance):\n\n- **AI & Automation Strategy Sessions:**  \n  60-minute consultation to identify automation opportunities.  \n  [https://booking.example.com/strategy-session](https://booking.example.com/strategy-session)\n\n- **AI Voice Agent Implementation:**  \n  End-to-end setup of AI phone agents to handle customer inquiries, bookings, and support 24/7.\n\n- **Process Automation:**  \n  Automate repetitive tasks such as invoice processing, CRM updates, and customer follow-ups.\n\n- **Custom AI Chatbots:**  \n  Build and deploy tailored chatbots to engage website visitors and improve lead conversion.\n\n---\n\n## ✅ Output Format:\n\nIf the lead is Cold:\n\n<p>Hi customer first name,</p>\n\n<p>Thanks for reaching out! We offer a range of AI and automation services designed to help streamline your business operations and boost productivity. Here’s an overview of how we can help:</p>\n\n<ul>\n  <li><strong>AI & Automation Strategy Sessions:</strong> 60-minute consultation to identify and prioritise automation opportunities tailored to your business.</li>\n  <li><strong>AI Voice Agent Implementation:</strong> AI phone agents that manage bookings, answer common questions, and provide support 24/7.</li>\n  <li><strong>Process Automation:</strong> Automate tasks like invoicing, CRM updates, and email follow-ups to save time and reduce errors.</li>\n  <li><strong>Custom AI Chatbots:</strong> Build and deploy chatbots that engage your website visitors and convert more leads.</li>\n</ul>\n\n<p>If you’d like to explore how these services can fit your needs, please book a free discovery call here:  \n<a href=\"https://booking.example.com/discovery-call\" target=\"_blank\">Book a Discovery Call</a></p>\n\n<p>Looking forward to connecting!</p>\n\nIf the lead is Warm/Hot and has mentioned a specific service (e.g., AI Voice Agent):\n\n<html>\n<p>Hi customer first name,</p>\n\n<p>Great to hear you're interested in our mentioned service! Here’s a bit more detail to help you get started:</p>\n\n<p><strong> service description</strong></p>\n\n<p>We’d love to discuss how this can be tailored to your business. Please book a time that suits you using the link below:</p>\n\n<p><a href=\"https://booking.example.com/strategy-session\" target=\"_blank\">Schedule a Strategy Session</a></p>\n\n<p>Looking forward to working with you!</p>\n</html>\n\n🛠 Service Descriptions (for AI use):\n\nAI & Automation Strategy Sessions:\n60-minute consultation to uncover and prioritise automation opportunities tailored to your workflows and goals.\n\nAI Voice Agent Implementation:\nFull-service AI voice agents that can answer inbound calls, manage bookings, and provide support around the clock, improving customer satisfaction and saving your team time.\n\nProcess Automation:\nAutomate repetitive tasks such as invoice processing, CRM data entry, and customer follow-ups to boost operational efficiency.\n\nCustom AI Chatbots:\nBespoke AI chatbots are designed to engage website visitors, answer questions, and drive conversions, fully integrated into your existing systems.\n\n🚫 Strict Rules:\n\n- Classify the lead as Cold, Warm, or Hot based on their message intent and tone.\n- For Cold leads: always include the full service overview + booking link.\n- For Warm/Hot leads: if a service is mentioned, focus the reply on that service with the relevant description + booking link.\n- Replace customer's first name with the recipient’s first name. You can find the customer's first name within here: {{ $('New Email').item.json.From }}. If not check the signoff of their email and If the name is still unavailable, default to: Hi there,\n- Replace the mentioned service and service description based on the specific service mentioned in the inquiry.\n- Always output raw HTML but no '''' or HTML\n\n📩 Inquiry to Analyse:\n{{ $('All Snippets').item.json['all snippets'] }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [2040, 880], "id": "547d0000-469d-4836-9d79-820c6a2f769b", "name": "Sales Message"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2500, 1040], "id": "6bfe3da1-d8df-4045-8aab-ea803e2a7cc5", "name": "Sum Request Brain", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2000, 1040], "id": "5a47c796-83a1-45ec-9e86-175f5dd26ffb", "name": "Sales Brain", "credentials": {"openAiApi": {"id": "pVebLmNMBp3DGLT3", "name": "Ops Management"}}}], "pinData": {"New Email": [{"json": {"id": "19686e9d2f0c6bd1", "threadId": "19686e9d2f0c6bd1", "snippet": "Hi there, Great to chat earlier this week - looking forwards to our next meeting - can you share your link for me to book some time in please Many Thanks", "payload": {"mimeType": "multipart/alternative"}, "sizeEstimate": 6905, "historyId": "1962972", "internalDate": "1746020160000", "labels": [{"id": "INBOX", "name": "INBOX"}, {"id": "CATEGORY_PERSONAL", "name": "CATEGORY_PERSONAL"}, {"id": "UNREAD", "name": "UNREAD"}], "From": "Sample User <<EMAIL>>", "Subject": "Next Meeting", "To": "<EMAIL>"}}]}, "connections": {"New Email": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Check Email": {"main": [[{"node": "Email Type", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Move to Loom", "type": "main", "index": 0}], [{"node": "Check Email", "type": "main", "index": 0}]]}, "Type of Message": {"ai_languageModel": [[{"node": "Status Validator", "type": "ai_languageModel", "index": 0}]]}, "Email Type": {"main": [[{"node": "<PERSON> as <PERSON>m", "type": "main", "index": 0}], [{"node": "Get Threads", "type": "main", "index": 0}], [{"node": "Get Threads", "type": "main", "index": 0}], [{"node": "Get Threads", "type": "main", "index": 0}], [{"node": "Get Threads", "type": "main", "index": 0}]]}, "Status Validator": {"main": [[{"node": "Validated Category", "type": "main", "index": 0}]]}, "Check Email Brain": {"ai_languageModel": [[{"node": "Check Email", "type": "ai_languageModel", "index": 0}]]}, "Get Threads": {"main": [[{"node": "All Snippets", "type": "main", "index": 0}]]}, "Response Brain": {"ai_languageModel": [[{"node": "Draft Response", "type": "ai_languageModel", "index": 0}]]}, "Draft Response": {"main": [[{"node": "Urgent?", "type": "main", "index": 0}]]}, "All Snippets": {"main": [[{"node": "Status Validator", "type": "main", "index": 0}]]}, "Send Email?": {"main": [[{"node": "Send URGENT Response", "type": "main", "index": 0}], [{"node": "Create URGENT Draft", "type": "main", "index": 0}]]}, "Validated Category": {"main": [[{"node": "Draft Response", "type": "main", "index": 0}], [{"node": "Draft Response", "type": "main", "index": 0}], [{"node": "Categorise Issue", "type": "main", "index": 0}], [{"node": "Sales Message", "type": "main", "index": 0}]]}, "Send URGENT Response": {"main": [[{"node": "Confirm URGENT Email Sent", "type": "main", "index": 0}]]}, "Create URGENT Draft": {"main": [[{"node": "Share URGENT Draft Link", "type": "main", "index": 0}]]}, "URGENT Request": {"main": [[{"node": "Send Email?", "type": "main", "index": 0}]]}, "Notify User": {"main": [[{"node": "Send Email?1", "type": "main", "index": 0}]]}, "Create Draft": {"main": [[{"node": "Share Draft Link", "type": "main", "index": 0}]]}, "Send Response": {"main": [[{"node": "Confirm Message Sent", "type": "main", "index": 0}]]}, "Send Email?1": {"main": [[{"node": "Send Response", "type": "main", "index": 0}], [{"node": "Create Draft", "type": "main", "index": 0}]]}, "Urgent?": {"main": [[{"node": "URGENT Request", "type": "main", "index": 0}], [{"node": "Notify User", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Categorise Issue", "type": "ai_languageModel", "index": 0}]]}, "Categorise Issue": {"main": [[{"node": "High Risk?", "type": "main", "index": 0}]]}, "High Risk?": {"main": [[{"node": "Notify Team To Respond", "type": "main", "index": 0}], [{"node": "Response Generator", "type": "main", "index": 0}]]}, "Q&A Brain": {"ai_languageModel": [[{"node": "QA Vector Store", "type": "ai_languageModel", "index": 0}]]}, "Small Embeddings": {"ai_embedding": [[{"node": "Pinecone Q&A Store", "type": "ai_embedding", "index": 0}]]}, "Pinecone Q&A Store": {"ai_vectorStore": [[{"node": "QA Vector Store", "type": "ai_vectorStore", "index": 0}]]}, "QA Vector Store": {"ai_tool": [[{"node": "Response Generator", "type": "ai_tool", "index": 0}]]}, "Response Generator": {"main": [[{"node": "Require Human?", "type": "main", "index": 0}]]}, "Require Human?": {"main": [[{"node": "Human Required", "type": "main", "index": 0}], [{"node": "Respond to Customer", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "On form submission": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}, "Respond to Lead": {"main": [[{"node": "Summarise Request", "type": "main", "index": 0}]]}, "Summarise Request": {"main": [[{"node": "Notify Team", "type": "main", "index": 0}]]}, "Sales Message": {"main": [[{"node": "Respond to Lead", "type": "main", "index": 0}]]}, "Sum Request Brain": {"ai_languageModel": [[{"node": "Summarise Request", "type": "ai_languageModel", "index": 0}]]}, "Sales Brain": {"ai_languageModel": [[{"node": "Sales Message", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4e911d88-b4e8-48ad-b6e3-2cd6e14a1f97", "meta": {"templateCredsSetupCompleted": true, "instanceId": "8e19fa74944b9dcd6868709031c9aa3bce12c850be41a7bacd11dbae58569d60"}, "id": "WyRivz63vhmFlZJe", "tags": []}