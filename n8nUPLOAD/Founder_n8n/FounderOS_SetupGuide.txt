# Getting Started

This guide will help you set up an end-to-end Meta ads automation system that:

- Automatically researches products and customer profiles
- Generates compelling ad angles and concepts
- Creates complete video sales letter (VSL) scripts
- Tracks ad performance metrics in real-time
- Identifies winning ads and applies their patterns to new campaigns

**Tools Required:**

- Airtable (for interface and database)
- n8n (for automation backend)
- Perplexity AI (for product research)
- Gemini 2.5 Pro (for creative generation)
- ScrapeFly (for web scraping)
- OpenAI (for transcription)
- Facebook Developer Account

## System Architecture

The system operates on three core layers:

1. **Front-End Interface**: Airtable provides a user-friendly dashboard where you control the process
2. **Automation Backend**: n8n workflows that handle all the integrations and AI processing
3. **Meta API Integration**: Direct connection to your Facebook ad accounts for data collection

Let's set up each component step by step.


# AIRTABLE SETUP

### Step 1: Import the Airtable Template

1. Go to https://airtable.com/appPfiL0Crk0reO9O/shrYSnhv53n0UZHWD
2. Click "Copy base" to import the template to your Airtable workspace
3. Once imported, you'll see the following tables:
    - Products
    - Ad Angles
    - Scripts
    - Ad Performance
    - Winning Scripts

### Step 2: Set Up Airtable Interface

The template includes a pre-built interface, but if you need to customize it:

1. Click "Interface" in the top right corner of Airtable
2. The interface contains 5 key sections:
    - Initial Product Research
    - Ad Angle Generation & Selection
    - Script Generation & Review
    - Performance Tracking
    - Winning Script Selection

### Step 3: Configure Airtable Automations

You'll need to set up 5 key automations in Airtable that will trigger your n8n workflows:

1. Go to "Automations" in Airtable
2. Create these 5 automations:
    - **Generate Product Details**: Triggers when "Generate Product Details" checkbox is checked
    - **Generate Product Angles**: Triggers when "Generate Angles" checkbox is checked
    - **Generate Scripts**: Triggers when "Generate Script" button is clicked
    - **Feedback Iteration**: Triggers when feedback is submitted
    - **Best Ads**: Triggers when an ad is marked as a "best ad"

For each automation:

1. Set the trigger condition (e.g., "When record matches conditions")
2. Add an action to update the status field (optional but helpful for UX)
3. Add a "Run Script" action to call your n8n webhook
4. Use this code pattern for the webhook call:


# N8N AUTOMATION BACKEND SETUP

### Step 1: Install and Set Up n8n

1. Sign up for n8n (Cloud) or install it locally following their documentation
2. Create a new workflow for each of the main components:
    - Product Research Workflow
    - Ad Angle Generation Workflow
    - Script Generation Workflow
    - Ad Performance Analytics Workflow
    - Best Ad Script Extraction Workflow

### Step 2: Set Up Webhooks

For each workflow:

1. Start with a "Webhook" node as the trigger
2. Configure it as a POST endpoint
3. Copy the generated webhook URL to use in your Airtable automations
4. Add a "Switch" node after the webhook to route based on the "action" parameter

Example Switch node setup:

- If `action = "generate_product_details"` → Route to product research flow
- If `action = "generate_angles"` → Route to angle generation flow
- If `action = "generate_script"` → Route to script generation flow
- If `action = "feedback"` → Route to script revision flow

### Step 3: Airtable Integration

For each workflow:

1. Add an "Airtable" node after your routing logic
2. Configure it to:
    - Connect to your Airtable account
    - Select the appropriate base and table
    - Use the "Get Record" operation
    - Use the recordId from the webhook payload

Example config for Product Research flow:

- Operation: "Get Record"
- Base ID: Your Airtable base ID
- Table: "Products"
- Record ID: `{{$json.recordId}}`

### Step 4: Set Up AI Service Nodes

Configure nodes for each AI service:

### Perplexity AI (for Research)

1. Add an "HTTP Request" node
2. Configure it for Perplexity API:
    - Method: POST
    - URL: `https://api.perplexity.ai/chat/completions`
    - Headers: Include your API key
    - Body: Include your research prompt with variables from Airtable

### Gemini (for Creative Content)

1. Add a "Google AI" node (or HTTP Request if n8n doesn't have a native integration)
2. Configure for Gemini 2.5 Pro:
    - Model: gemini-pro
    - Include your prompt templates for angle/script generation
    - Pass variables from Airtable as context

### OpenAI (for Transcription)

1. Add an "OpenAI" node
2. Configure for transcription:
    - Operation: "Audio Transcription"
    - Audio URL: The extracted video URL
    - Model: "whisper-1"

### Step 5: Output Handling

For each workflow:

1. Add a "Set" node to format the AI outputs properly
2. Add an "Airtable" node to update the corresponding record:
    - Operation: "Update Record"
    - Base ID: Your Airtable base ID
    - Table: Corresponding table (Products/Angles/Scripts)
    - Record ID: The original recordId
    - Fields to update: The AI-generated content


    # META API INTEGRATION SETUP

### Step 1: Create a Meta Developer App

1. Go to [developers.facebook.com](https://developers.facebook.com/)
2. Log in with your Facebook account
3. Go to "My Apps" and click "Create App"
4. Select "Business" as the app type
5. Name your app (e.g., "Founder CreativeOS")
6. Select your business account
7. Click "Create App"

### Step 2: Set Up Marketing API

1. In your app dashboard, find "Add Products" and select "Marketing API"
2. Click "Set Up"
3. Grant the following permissions:
    - ads_management
    - ads_read
    - read_insights
4. Generate an access token with these permissions
5. Save this token for use in n8n

### Step 3: Configure the Meta API Workflow in n8n

1. Create a new workflow for ad performance tracking
2. Start with a "Schedule Trigger" node (daily or weekly execution)
3. Add an "HTTP Request" node for the Graph API:
    - Method: GET
    - URL: `https://graph.facebook.com/v18.0/me/adaccounts`
    - Headers: Include your access token
4. Add a "Split In Batches" node to iterate through accounts
5. Add additional HTTP nodes to:
    - Get campaigns
    - Get ad sets
    - Get ads
    - Get insights
6. Add filters to only process active campaigns
7. Format the data with "Set" nodes
8. Update Airtable with the "Airtable" node

### Step 4: Set Up the Ad Script Extraction Flow

1. Create a workflow triggered by the "Best Ads" webhook
2. Get the ad ID from Airtable
3. Request ad details via Graph API:
    - URL: `https://graph.facebook.com/v18.0/{ad_id}`
    - Include fields=permalink_url
4. Add a "ScrapeFly" node to scrape the ad page
5. Extract the video URL using a "Code" node with this pattern:

```jsx

javascript
const htmlContent = $input.item.json.html;
const videoUrlRegex = /videoURL:"([^"]+)"/;
const match = htmlContent.match(videoUrlRegex);

if (match && match[1]) {
  return {
    videoUrl: match[1]
  };
}
return { videoUrl: null };

```

1. Use an "HTTP Request" node to download the video
2. Use the "OpenAI" node to transcribe the audio
3. Update the "Winning Scripts" table in Airtable


# META API INTEGRATION SETUP

### Step 1: Create a Meta Developer App

1. Go to [developers.facebook.com](https://developers.facebook.com/)
2. Log in with your Facebook account
3. Go to "My Apps" and click "Create App"
4. Select "Business" as the app type
5. Name your app (e.g., "Founder CreativeOS")
6. Select your business account
7. Click "Create App"

### Step 2: Set Up Marketing API

1. In your app dashboard, find "Add Products" and select "Marketing API"
2. Click "Set Up"
3. Grant the following permissions:
    - ads_management
    - ads_read
    - read_insights
4. Generate an access token with these permissions
5. Save this token for use in n8n

### Step 3: Configure the Meta API Workflow in n8n

1. Create a new workflow for ad performance tracking
2. Start with a "Schedule Trigger" node (daily or weekly execution)
3. Add an "HTTP Request" node for the Graph API:
    - Method: GET
    - URL: `https://graph.facebook.com/v18.0/me/adaccounts`
    - Headers: Include your access token
4. Add a "Split In Batches" node to iterate through accounts
5. Add additional HTTP nodes to:
    - Get campaigns
    - Get ad sets
    - Get ads
    - Get insights
6. Add filters to only process active campaigns
7. Format the data with "Set" nodes
8. Update Airtable with the "Airtable" node

### Step 4: Set Up the Ad Script Extraction Flow

1. Create a workflow triggered by the "Best Ads" webhook
2. Get the ad ID from Airtable
3. Request ad details via Graph API:
    - URL: `https://graph.facebook.com/v18.0/{ad_id}`
    - Include fields=permalink_url
4. Add a "ScrapeFly" node to scrape the ad page
5. Extract the video URL using a "Code" node with this pattern:

```jsx

javascript
const htmlContent = $input.item.json.html;
const videoUrlRegex = /videoURL:"([^"]+)"/;
const match = htmlContent.match(videoUrlRegex);

if (match && match[1]) {
  return {
    videoUrl: match[1]
  };
}
return { videoUrl: null };

```

1. Use an "HTTP Request" node to download the video
2. Use the "OpenAI" node to transcribe the audio
3. Update the "Winning Scripts" table in Airtable


PROMPT ENGINEERING
Product Research Prompt
Use this prompt template for the Perplexity AI node:


I need a comprehensive analysis of the following product for creating effective Meta ads.

Product: {{$node["Airtable"].json["fields"]["Product Name"]}}
Notes: {{$node["Airtable"].json["fields"]["Product Notes"]}}

Please provide a detailed analysis with the following sections:

1. PRODUCT DEEP DIVE
- Core features and benefits
- Key specifications
- Unique selling propositions
- Pricing structure
- Target market positioning

2. CORE CUSTOMER PROFILE
- Detailed demographic information
- Psychographic characteristics
- Pain points this product solves
- Purchase motivations
- Objections they might have

3. BRAND IDENTITY
- Brand voice and positioning
- Core values
- How it differentiates from competitors

Format your response as a detailed report that will be used to inform ad creative development.

​
Ad Angle Generation Prompt
Use this prompt for the Gemini node:


You are an expert VSL (Video Sales Letter) ad angle strategist for Meta ads.

PRODUCT INFORMATION:
{{$node["Airtable"].json["fields"]["Product Details"]}}

CUSTOMER PROFILE:
{{$node["Airtable"].json["fields"]["Customer Profile"]}}

ADDITIONAL NOTES:
{{$node["Airtable"].json["fields"]["Product Notes"]}}

WINNING SCRIPT TO REFERENCE (if available):
{{$node["Airtable"].json["fields"]["Winning Script"]}}

Create 5-8 distinct ad angles for video ads. Each angle should include:

1. ANGLE NAME: A catchy, descriptive title
2. CONCEPT SUMMARY: 2-3 sentences explaining the core concept
3. VSL STRUCTURE:
   - Hook (attention grabber)
   - Problem (pain point identification)
   - Agitation (why it matters)
   - Solution introduction
   - Proof/Credibility
   - Offer/CTA
4. VIDEO SUGGESTIONS: Visual ideas for the ad
5. EMOTIONAL TRIGGERS: Primary emotions targeted
6. COGNITIVE BIASES: Which psychological biases this leverages
7. DIRECT RESPONSE TECHNIQUES: Sales techniques employed

Format each angle as a separate JSON object in an array.

​
Script Generation Prompt
Use this prompt for script generation:


You are an expert VSL (Video Sales Letter) scriptwriter for Meta ads.

PRODUCT INFORMATION:
{{$node["Airtable"].json["fields"]["Product Details"]}}

ANGLE CONCEPT:
{{$node["Airtable"].json["fields"]["Angle Concept"]}}

VSL STRUCTURE:
{{$node["Airtable"].json["fields"]["VSL Structure"]}}

TARGET LENGTH:
{{$node["Airtable"].json["fields"]["Script Length"]}}

Write a complete, ready-to-record VSL script following this exact structure:

1. HOOK: An attention-grabbing opening line (5-10 seconds)
2. PROBLEM: Clearly state the pain point (10-15 seconds)
3. AGITATION: Why this problem matters (15-20 seconds)
4. SOLUTION INTRO: Introduce your product as the answer (10-15 seconds)
5. PROOF/CREDIBILITY: Evidence it works (20-30 seconds)
6. BENEFITS: What they'll gain (20-30 seconds)
7. OFFER: Clear presentation of what they'll get (15-20 seconds)
8. CTA: Exactly what they should do next (10-15 seconds)

Format the script with these section headings and make it conversational, engaging, and emotionally compelling. The total script should be appropriate for a {{$node["Airtable"].json["fields"]["Script Length"]}} video.



# OPTIMIZATION FLYWHEEL

To complete the system setup, implement this optimization process:

1. **Identify Winning Ads**:
    - Define what constitutes a "winning ad" (e.g., ROAS > 2, Conversion Rate > 5%)
    - Mark these in the Ad Performance table
2. **Extract Winning Patterns**:
    - Use the Best Ad Script Extraction workflow to scrape and transcribe top performers
    - Store these in the Winning Scripts table
3. **Apply to New Products**:
    - In the Airtable interface, assign winning scripts to new products
    - This feeds into the angle generation process for new products
4. **Continuous Improvement**:
    - The system gets smarter with each campaign
    - Regularly review performance data to identify trends
    - Update your prompts based on what's working


    # Troubleshooting Common Issues

### Airtable Webhook Issues

- Ensure webhook URLs are correctly copied to Airtable automations
- Check that your Airtable API key has sufficient permissions

### n8n Connection Problems

- Verify all API credentials are valid and up to date
- Check node configurations for any missing required fields

### Meta API Limitations

- Be aware of rate limits (implement delays between calls)
- Ensure your access token has the necessary permissions
- Regularly refresh long-lived tokens

### AI Generation Quality

- Refine prompts if outputs are not specific enough
- Add more context about your specific industry or product type
- Use the feedback loop to iterate on scripts


# Next Steps and Advanced Features

Once your basic system is running, consider these enhancements:

1. **Automated Video Creation**:
    - Integrate with Hey Gen or similar AI video generation tools
    - Create a workflow that sends winning scripts to video generation
2. **A/B Testing Automation**:
    - Set up workflows that automatically create variants of top performers
    - Implement statistical analysis to identify significant differences
3. **Audience Insights Integration**:
    - Pull in audience data from Meta to refine customer profiles
    - Create dynamic customer segments based on engagement
4. **Multi-Platform Expansion**:
    - Adapt the system for other ad platforms (Google, TikTok, etc.)
    - Create platform-specific creative variations